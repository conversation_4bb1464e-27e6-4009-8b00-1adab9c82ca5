export interface Merchant {
  merchant_id?: number;
  merchant_name: string;
  merchant_vat?: string;
  merchant_type: 'main' | 'sub';
  parent_merchant_id?: number;
  parent_merchant_name?: string;
  merchant_sub_name?: string;
  merchant_mcc?: string;
  merchant_id_wechat?: string;
  phone?: string;
  email?: string;
  address?: string;
  zipcode?: string;
  remark?: string;
  invoice_name?: string;
  invoice_tax?: string;
  invoice_address?: string;
  contact_person?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_fax?: string;
  group_id?: number;
  zone_id?: number;
  product_id?: number;
  category_id?: number;
  rate_min_transfer?: number;
  /** Transfer Fee - Direct fee amount used in Final Net Amount calculation */
  transfer_fee?: number;
  /** Settlement Fee - Fee for settlement processing */
  settlement_fee?: number;
  /** Withholding Tax Rate (%) - Used to calculate withholding tax on MDR amount */
  withholding_tax?: number;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

export interface MerchantBank {
  merchant_bank_id?: number;
  merchant_id: number;
  bank_id: number;
  bank_code?: string;
  bank_name_th?: string;
  bank_name_en?: string;
  bank_account_no: string;
  bank_account_name?: string;
  bank_branch_name?: string;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

export interface MerchantWechat {
  merchant_wechat_id?: number;
  merchant_id: number;
  /** WeChat Pay MDR Rate (%) - Used to calculate MDR Amount = Transaction Amount × (MDR% ÷ 100) */
  wechat_rate?: number;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

export interface MerchantUnionpay {
  merchant_unionpay_id?: number;
  merchant_id: number;
  mdr_local?: number;
  mdr_normal?: number;
  mdr_premium?: number;
  mdr_diamond?: number;
  mdr_qrcode?: number;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

export interface Bank {
  bank_id: number;
  bank_code: string;
  bank_name_th?: string;
  bank_name_en?: string;
  active: boolean;
}

export interface Group {
  group_id: number;
  group_name: string;
  active: boolean;
}

export interface Zone {
  zone_id: number;
  zone_code: string;
  zone_name: string;
  active: boolean;
}

export interface Product {
  product_id: number;
  product_name: string;
  active: boolean;
}

export interface Category {
  category_id: number;
  category_name: string;
  active: boolean;
}
