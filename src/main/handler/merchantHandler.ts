import { ipcMain } from 'electron';
import { Client } from 'pg';
import { getDbConnection } from '../db';

interface Merchant {
  merchant_id?: number;
  merchant_name: string;
  merchant_vat?: string;
  merchant_type: 'main' | 'sub';
  parent_merchant_id?: number;
  parent_merchant_name?: string;
  merchant_sub_name?: string;
  merchant_mcc?: string;
  merchant_id_wechat?: string;
  phone?: string;
  email?: string;
  address?: string;
  zipcode?: string;
  remark?: string;
  invoice_name?: string;
  invoice_tax?: string;
  invoice_address?: string;
  contact_person?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_fax?: string;
  group_id?: number;
  zone_id?: number;
  product_id?: number;
  category_id?: number;
  rate_min_transfer?: number;
  transfer_fee?: number;
  settlement_fee?: number;
  withholding_tax?: number;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface MerchantBank {
  merchant_bank_id?: number;
  merchant_id: number;
  bank_id: number;
  bank_account_no: string;
  bank_account_name?: string;
  bank_branch_name?: string;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface MerchantWechat {
  merchant_wechat_id?: number;
  merchant_id: number;
  wechat_rate?: number;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface MerchantUnionpay {
  merchant_unionpay_id?: number;
  merchant_id: number;
  mdr_local?: number;
  mdr_normal?: number;
  mdr_premium?: number;
  mdr_diamond?: number;
  mdr_qrcode?: number;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface MerchantResponse {
  success: boolean;
  message: string;
  data?: Merchant | Merchant[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  error?: string;
}

interface PaginationParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export function setupMerchantHandlers() {
  console.log('Setting up Merchant handlers...');

  // Get merchants with pagination
  ipcMain.handle('get-merchants', async (_event, params: PaginationParams = {}): Promise<MerchantResponse> => {
    let client: Client | null = null;

    try {
      const {
        page = 1,
        pageSize = 10,
        search = '',
        sortBy = 'merchant_id',
        sortOrder = 'ASC'
      } = params;

      console.log(`📋 Fetching merchants - Page: ${page}, Size: ${pageSize}, Search: "${search}"`);
      client = await getDbConnection();

      let whereClause = '';
      let searchParams: any[] = [];
      let paramIndex = 1;

      if (search.trim()) {
        whereClause = `WHERE (
          UPPER(m.merchant_name) LIKE UPPER($${paramIndex}) OR
          UPPER(m.merchant_vat) LIKE UPPER($${paramIndex + 1}) OR
          UPPER(m.email) LIKE UPPER($${paramIndex + 2}) OR
          UPPER(m.phone) LIKE UPPER($${paramIndex + 3})
        )`;
        searchParams = [`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`];
        paramIndex += 4;
      }

      // Count total records
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM merchant m
        LEFT JOIN merchant pm ON m.parent_merchant_id = pm.merchant_id
        ${whereClause}
      `;
      const countResult = await client.query(countQuery, searchParams);
      const totalRecords = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(totalRecords / pageSize);
      const offset = (page - 1) * pageSize;
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      // Validate sort column
      const validSortColumns = ['merchant_id', 'merchant_name', 'merchant_type', 'email', 'phone', 'active', 'create_dt'];
      const validSortBy = validSortColumns.includes(sortBy) ? sortBy : 'merchant_id';
      const validSortOrder = sortOrder === 'DESC' ? 'DESC' : 'ASC';

      const dataQuery = `
        SELECT m.merchant_id, m.merchant_name, m.merchant_vat, m.merchant_type, m.parent_merchant_id,
               pm.merchant_name as parent_merchant_name, m.merchant_sub_name,
               m.merchant_mcc, m.merchant_id_wechat, m.phone, m.email, m.address, m.zipcode, m.remark,
               m.invoice_name, m.invoice_tax, m.invoice_address, m.contact_person, m.contact_email,
               m.contact_phone, m.contact_fax, m.group_id, m.zone_id, m.product_id, m.category_id,
               m.rate_min_transfer, m.transfer_fee, m.settlement_fee, m.withholding_tax, m.active,
               m.create_by, m.create_dt, m.update_by, m.update_dt
        FROM merchant m
        LEFT JOIN merchant pm ON m.parent_merchant_id = pm.merchant_id
        ${whereClause}
        ORDER BY m.${validSortBy} ${validSortOrder}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const dataParams = [...searchParams, pageSize, offset];
      const result = await client.query(dataQuery, dataParams);

      console.log(`✅ Found ${result.rows.length} merchants (Page ${page}/${totalPages}, Total: ${totalRecords})`);

      return {
        success: true,
        message: `Found ${result.rows.length} merchants on page ${page}`,
        data: result.rows,
        pagination: {
          currentPage: page,
          totalPages,
          totalRecords,
          pageSize,
          hasNextPage,
          hasPreviousPage
        }
      };

    } catch (error: any) {
      console.error('❌ Error fetching merchants:', error.message);
      return {
        success: false,
        message: 'Failed to fetch merchants',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get main merchants for dropdown (used when creating sub merchants)
  ipcMain.handle('get-main-merchants', async (): Promise<MerchantResponse> => {
    let client: Client | null = null;

    try {
      console.log('📋 Fetching main merchants for dropdown');
      client = await getDbConnection();

      const query = `
        SELECT merchant_id, merchant_name
        FROM merchant
        WHERE merchant_type = 'main' AND active = true
        ORDER BY merchant_name ASC
      `;

      const result = await client.query(query);
      console.log(`✅ Found ${result.rows.length} main merchants`);

      return {
        success: true,
        message: `Found ${result.rows.length} main merchants`,
        data: result.rows
      };

    } catch (error: any) {
      console.error('❌ Error fetching main merchants:', error.message);
      return {
        success: false,
        message: 'Failed to fetch main merchants',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Create merchant
  ipcMain.handle('create-merchant', async (_event, merchantData: Omit<Merchant, 'merchant_id' | 'create_dt' | 'update_dt'>): Promise<MerchantResponse> => {
    let client: Client | null = null;

    try {
      console.log('➕ Creating new merchant:', merchantData.merchant_name);
      client = await getDbConnection();

      const query = `
        INSERT INTO merchant (
          merchant_name, merchant_vat, merchant_type, parent_merchant_id, merchant_sub_name, merchant_mcc,
          merchant_id_wechat, phone, email, address, zipcode, remark, invoice_name,
          invoice_tax, invoice_address, contact_person, contact_email, contact_phone,
          contact_fax, group_id, zone_id, product_id, category_id, rate_min_transfer,
          transfer_fee, settlement_fee, withholding_tax, active, create_by
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29)
        RETURNING merchant_id, merchant_name, merchant_vat, merchant_type, parent_merchant_id,
                  merchant_sub_name, merchant_mcc, merchant_id_wechat, phone, email, address, zipcode, remark,
                  invoice_name, invoice_tax, invoice_address, contact_person, contact_email,
                  contact_phone, contact_fax, group_id, zone_id, product_id, category_id,
                  rate_min_transfer, transfer_fee, settlement_fee, withholding_tax, active,
                  create_by, create_dt, update_by, update_dt
      `;

      const values = [
        merchantData.merchant_name,
        merchantData.merchant_vat || null,
        merchantData.merchant_type,
        merchantData.parent_merchant_id || null,
        merchantData.merchant_sub_name || null,
        merchantData.merchant_mcc || null,
        merchantData.merchant_id_wechat || null,
        merchantData.phone || null,
        merchantData.email || null,
        merchantData.address || null,
        merchantData.zipcode || null,
        merchantData.remark || null,
        merchantData.invoice_name || null,
        merchantData.invoice_tax || null,
        merchantData.invoice_address || null,
        merchantData.contact_person || null,
        merchantData.contact_email || null,
        merchantData.contact_phone || null,
        merchantData.contact_fax || null,
        merchantData.group_id || null,
        merchantData.zone_id || null,
        merchantData.product_id || null,
        merchantData.category_id || null,
        merchantData.rate_min_transfer || null,
        merchantData.transfer_fee || null,
        merchantData.settlement_fee || null,
        merchantData.withholding_tax || null,
        merchantData.active,
        merchantData.create_by || 'SYSTEM'
      ];

      const result = await client.query(query, values);
      console.log('✅ Merchant created successfully:', result.rows[0].merchant_name);

      return {
        success: true,
        message: 'Merchant created successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error creating merchant:', error.message);

      let errorMessage = 'Failed to create merchant';
      if (error.code === '23505') {
        errorMessage = 'Merchant with this information already exists';
      } else if (error.code === '23514') {
        errorMessage = 'Invalid merchant type. Must be "main" or "sub"';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Update merchant
  ipcMain.handle('update-merchant', async (_event, merchantId: number, merchantData: Omit<Merchant, 'merchant_id' | 'create_dt' | 'create_by'>): Promise<MerchantResponse> => {
    let client: Client | null = null;

    try {
      console.log('✏️ Updating merchant ID:', merchantId);
      client = await getDbConnection();

      const query = `
        UPDATE merchant
        SET merchant_name = $1, merchant_vat = $2, merchant_type = $3, parent_merchant_id = $4,
            merchant_sub_name = $5, merchant_mcc = $6, merchant_id_wechat = $7, phone = $8, email = $9, address = $10,
            zipcode = $11, remark = $12, invoice_name = $13, invoice_tax = $14,
            invoice_address = $15, contact_person = $16, contact_email = $17,
            contact_phone = $18, contact_fax = $19, group_id = $20, zone_id = $21,
            product_id = $22, category_id = $23, rate_min_transfer = $24,
            transfer_fee = $25, settlement_fee = $26, withholding_tax = $27, active = $28,
            update_by = $29, update_dt = CURRENT_TIMESTAMP
        WHERE merchant_id = $30
        RETURNING merchant_id, merchant_name, merchant_vat, merchant_type, parent_merchant_id,
                  merchant_sub_name, merchant_mcc, merchant_id_wechat, phone, email, address, zipcode, remark,
                  invoice_name, invoice_tax, invoice_address, contact_person, contact_email,
                  contact_phone, contact_fax, group_id, zone_id, product_id, category_id,
                  rate_min_transfer, transfer_fee, settlement_fee, withholding_tax, active,
                  create_by, create_dt, update_by, update_dt
      `;

      const values = [
        merchantData.merchant_name,
        merchantData.merchant_vat || null,
        merchantData.merchant_type,
        merchantData.parent_merchant_id || null,
        merchantData.merchant_sub_name || null,
        merchantData.merchant_mcc || null,
        merchantData.merchant_id_wechat || null,
        merchantData.phone || null,
        merchantData.email || null,
        merchantData.address || null,
        merchantData.zipcode || null,
        merchantData.remark || null,
        merchantData.invoice_name || null,
        merchantData.invoice_tax || null,
        merchantData.invoice_address || null,
        merchantData.contact_person || null,
        merchantData.contact_email || null,
        merchantData.contact_phone || null,
        merchantData.contact_fax || null,
        merchantData.group_id || null,
        merchantData.zone_id || null,
        merchantData.product_id || null,
        merchantData.category_id || null,
        merchantData.rate_min_transfer || null,
        merchantData.transfer_fee || null,
        merchantData.settlement_fee || null,
        merchantData.withholding_tax || null,
        merchantData.active,
        merchantData.update_by || 'SYSTEM',
        merchantId
      ];

      const result = await client.query(query, values);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'Merchant not found',
          error: 'MERCHANT_NOT_FOUND'
        };
      }

      console.log('✅ Merchant updated successfully:', result.rows[0].merchant_name);

      return {
        success: true,
        message: 'Merchant updated successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error updating merchant:', error.message);

      let errorMessage = 'Failed to update merchant';
      if (error.code === '23505') {
        errorMessage = 'Merchant with this information already exists';
      } else if (error.code === '23514') {
        errorMessage = 'Invalid merchant type. Must be "main" or "sub"';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Delete merchant
  ipcMain.handle('delete-merchant', async (_event, merchantId: number): Promise<MerchantResponse> => {
    let client: Client | null = null;

    try {
      console.log('🗑️ Deleting merchant ID:', merchantId);
      client = await getDbConnection();

      const query = `
        DELETE FROM merchant
        WHERE merchant_id = $1
        RETURNING merchant_name
      `;

      const result = await client.query(query, [merchantId]);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'Merchant not found',
          error: 'MERCHANT_NOT_FOUND'
        };
      }

      console.log('✅ Merchant deleted successfully:', result.rows[0].merchant_name);

      return {
        success: true,
        message: 'Merchant deleted successfully'
      };

    } catch (error: any) {
      console.error('❌ Error deleting merchant:', error.message);

      let errorMessage = 'Failed to delete merchant';
      if (error.code === '23503') {
        errorMessage = 'Cannot delete merchant. It has related records (bank accounts, WeChat, or UnionPay settings)';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get merchant banks
  ipcMain.handle('get-merchant-banks', async (_event, merchantId: number): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log(`📋 Fetching banks for merchant ID: ${merchantId}`);
      client = await getDbConnection();

      const query = `
        SELECT mb.merchant_bank_id, mb.merchant_id, mb.bank_id, mb.bank_account_no,
               mb.bank_account_name, mb.bank_branch_name, mb.active,
               mb.create_by, mb.create_dt, mb.update_by, mb.update_dt,
               b.bank_name_th, b.bank_name_en, b.bank_code
        FROM merchant_bank mb
        LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id
        WHERE mb.merchant_id = $1
        ORDER BY mb.merchant_bank_id
      `;

      const result = await client.query(query, [merchantId]);

      console.log(`✅ Found ${result.rows.length} bank accounts for merchant ${merchantId}`);

      return {
        success: true,
        message: `Found ${result.rows.length} bank accounts`,
        data: result.rows
      };

    } catch (error: any) {
      console.error('❌ Error fetching merchant banks:', error.message);
      return {
        success: false,
        message: 'Failed to fetch merchant banks',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Create merchant bank
  ipcMain.handle('create-merchant-bank', async (_event, bankData: Omit<MerchantBank, 'merchant_bank_id' | 'create_dt' | 'update_dt'>): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log('➕ Creating new merchant bank account for merchant:', bankData.merchant_id);
      client = await getDbConnection();

      const query = `
        INSERT INTO merchant_bank (merchant_id, bank_id, bank_account_no, bank_account_name, bank_branch_name, active, create_by)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING merchant_bank_id, merchant_id, bank_id, bank_account_no,
                  bank_account_name, bank_branch_name, active,
                  create_by, create_dt, update_by, update_dt
      `;

      const values = [
        bankData.merchant_id,
        bankData.bank_id,
        bankData.bank_account_no,
        bankData.bank_account_name || null,
        bankData.bank_branch_name || null,
        bankData.active,
        bankData.create_by || 'SYSTEM'
      ];

      const result = await client.query(query, values);
      console.log('✅ Merchant bank account created successfully');

      return {
        success: true,
        message: 'Bank account created successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error creating merchant bank:', error.message);

      let errorMessage = 'Failed to create bank account';
      if (error.code === '23505') {
        errorMessage = 'Bank account number already exists for this merchant';
      } else if (error.code === '23503') {
        errorMessage = 'Invalid merchant or bank ID';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Update merchant bank
  ipcMain.handle('update-merchant-bank', async (_event, bankId: number, bankData: Omit<MerchantBank, 'merchant_bank_id' | 'create_dt' | 'create_by'>): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log('✏️ Updating merchant bank ID:', bankId);
      client = await getDbConnection();

      const query = `
        UPDATE merchant_bank
        SET merchant_id = $1, bank_id = $2, bank_account_no = $3,
            bank_account_name = $4, bank_branch_name = $5, active = $6,
            update_by = $7, update_dt = CURRENT_TIMESTAMP
        WHERE merchant_bank_id = $8
        RETURNING merchant_bank_id, merchant_id, bank_id, bank_account_no,
                  bank_account_name, bank_branch_name, active,
                  create_by, create_dt, update_by, update_dt
      `;

      const values = [
        bankData.merchant_id,
        bankData.bank_id,
        bankData.bank_account_no,
        bankData.bank_account_name || null,
        bankData.bank_branch_name || null,
        bankData.active,
        bankData.update_by || 'SYSTEM',
        bankId
      ];

      const result = await client.query(query, values);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'Bank account not found',
          error: 'BANK_ACCOUNT_NOT_FOUND'
        };
      }

      console.log('✅ Merchant bank account updated successfully');

      return {
        success: true,
        message: 'Bank account updated successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error updating merchant bank:', error.message);

      let errorMessage = 'Failed to update bank account';
      if (error.code === '23505') {
        errorMessage = 'Bank account number already exists for this merchant';
      } else if (error.code === '23503') {
        errorMessage = 'Invalid merchant or bank ID';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Delete merchant bank
  ipcMain.handle('delete-merchant-bank', async (_event, bankId: number): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log('🗑️ Deleting merchant bank ID:', bankId);
      client = await getDbConnection();

      const query = `
        DELETE FROM merchant_bank
        WHERE merchant_bank_id = $1
        RETURNING bank_account_no
      `;

      const result = await client.query(query, [bankId]);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'Bank account not found',
          error: 'BANK_ACCOUNT_NOT_FOUND'
        };
      }

      console.log('✅ Merchant bank account deleted successfully');

      return {
        success: true,
        message: 'Bank account deleted successfully'
      };

    } catch (error: any) {
      console.error('❌ Error deleting merchant bank:', error.message);
      return {
        success: false,
        message: 'Failed to delete bank account',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get merchant wechat
  ipcMain.handle('get-merchant-wechat', async (_event, merchantId: number): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log(`📋 Fetching WeChat settings for merchant ID: ${merchantId}`);
      client = await getDbConnection();

      const query = `
        SELECT merchant_wechat_id, merchant_id, wechat_rate, active,
               create_by, create_dt, update_by, update_dt
        FROM merchant_wechat
        WHERE merchant_id = $1
        ORDER BY merchant_wechat_id
      `;

      const result = await client.query(query, [merchantId]);

      console.log(`✅ Found ${result.rows.length} WeChat settings for merchant ${merchantId}`);

      return {
        success: true,
        message: `Found ${result.rows.length} WeChat settings`,
        data: result.rows
      };

    } catch (error: any) {
      console.error('❌ Error fetching merchant WeChat:', error.message);
      return {
        success: false,
        message: 'Failed to fetch merchant WeChat settings',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Create merchant wechat
  ipcMain.handle('create-merchant-wechat', async (_event, wechatData: Omit<MerchantWechat, 'merchant_wechat_id' | 'create_dt' | 'update_dt'>): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log('➕ Creating new merchant WeChat settings for merchant:', wechatData.merchant_id);
      client = await getDbConnection();

      const query = `
        INSERT INTO merchant_wechat (merchant_id, wechat_rate, active, create_by)
        VALUES ($1, $2, $3, $4)
        RETURNING merchant_wechat_id, merchant_id, wechat_rate, active,
                  create_by, create_dt, update_by, update_dt
      `;

      const values = [
        wechatData.merchant_id,
        wechatData.wechat_rate || null,
        wechatData.active,
        wechatData.create_by || 'SYSTEM'
      ];

      const result = await client.query(query, values);
      console.log('✅ Merchant WeChat settings created successfully');

      return {
        success: true,
        message: 'WeChat settings created successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error creating merchant WeChat:', error.message);

      let errorMessage = 'Failed to create WeChat settings';
      if (error.code === '23503') {
        errorMessage = 'Invalid merchant ID';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Update merchant wechat
  ipcMain.handle('update-merchant-wechat', async (_event, wechatId: number, wechatData: Omit<MerchantWechat, 'merchant_wechat_id' | 'create_dt' | 'create_by'>): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log('✏️ Updating merchant WeChat ID:', wechatId);
      client = await getDbConnection();

      const query = `
        UPDATE merchant_wechat
        SET merchant_id = $1, wechat_rate = $2, active = $3,
            update_by = $4, update_dt = CURRENT_TIMESTAMP
        WHERE merchant_wechat_id = $5
        RETURNING merchant_wechat_id, merchant_id, wechat_rate, active,
                  create_by, create_dt, update_by, update_dt
      `;

      const values = [
        wechatData.merchant_id,
        wechatData.wechat_rate || null,
        wechatData.active,
        wechatData.update_by || 'SYSTEM',
        wechatId
      ];

      const result = await client.query(query, values);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'WeChat settings not found',
          error: 'WECHAT_SETTINGS_NOT_FOUND'
        };
      }

      console.log('✅ Merchant WeChat settings updated successfully');

      return {
        success: true,
        message: 'WeChat settings updated successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error updating merchant WeChat:', error.message);

      let errorMessage = 'Failed to update WeChat settings';
      if (error.code === '23503') {
        errorMessage = 'Invalid merchant ID';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Delete merchant wechat
  ipcMain.handle('delete-merchant-wechat', async (_event, wechatId: number): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log('🗑️ Deleting merchant WeChat ID:', wechatId);
      client = await getDbConnection();

      const query = `
        DELETE FROM merchant_wechat
        WHERE merchant_wechat_id = $1
        RETURNING merchant_wechat_id
      `;

      const result = await client.query(query, [wechatId]);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'WeChat settings not found',
          error: 'WECHAT_SETTINGS_NOT_FOUND'
        };
      }

      console.log('✅ Merchant WeChat settings deleted successfully');

      return {
        success: true,
        message: 'WeChat settings deleted successfully'
      };

    } catch (error: any) {
      console.error('❌ Error deleting merchant WeChat:', error.message);
      return {
        success: false,
        message: 'Failed to delete WeChat settings',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get merchant unionpay
  ipcMain.handle('get-merchant-unionpay', async (_event, merchantId: number): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log(`📋 Fetching UnionPay settings for merchant ID: ${merchantId}`);
      client = await getDbConnection();

      const query = `
        SELECT merchant_unionpay_id, merchant_id, mdr_local, mdr_normal, mdr_premium,
               mdr_diamond, mdr_qrcode, active, create_by, create_dt, update_by, update_dt
        FROM merchant_unionpay
        WHERE merchant_id = $1
        ORDER BY merchant_unionpay_id
      `;

      const result = await client.query(query, [merchantId]);

      console.log(`✅ Found ${result.rows.length} UnionPay settings for merchant ${merchantId}`);

      return {
        success: true,
        message: `Found ${result.rows.length} UnionPay settings`,
        data: result.rows
      };

    } catch (error: any) {
      console.error('❌ Error fetching merchant UnionPay:', error.message);
      return {
        success: false,
        message: 'Failed to fetch merchant UnionPay settings',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Create merchant unionpay
  ipcMain.handle('create-merchant-unionpay', async (_event, unionpayData: Omit<MerchantUnionpay, 'merchant_unionpay_id' | 'create_dt' | 'update_dt'>): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log('➕ Creating new merchant UnionPay settings for merchant:', unionpayData.merchant_id);
      client = await getDbConnection();

      const query = `
        INSERT INTO merchant_unionpay (merchant_id, mdr_local, mdr_normal, mdr_premium, mdr_diamond, mdr_qrcode, active, create_by)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING merchant_unionpay_id, merchant_id, mdr_local, mdr_normal, mdr_premium,
                  mdr_diamond, mdr_qrcode, active, create_by, create_dt, update_by, update_dt
      `;

      const values = [
        unionpayData.merchant_id,
        unionpayData.mdr_local || null,
        unionpayData.mdr_normal || null,
        unionpayData.mdr_premium || null,
        unionpayData.mdr_diamond || null,
        unionpayData.mdr_qrcode || null,
        unionpayData.active,
        unionpayData.create_by || 'SYSTEM'
      ];

      const result = await client.query(query, values);
      console.log('✅ Merchant UnionPay settings created successfully');

      return {
        success: true,
        message: 'UnionPay settings created successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error creating merchant UnionPay:', error.message);

      let errorMessage = 'Failed to create UnionPay settings';
      if (error.code === '23503') {
        errorMessage = 'Invalid merchant ID';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Update merchant unionpay
  ipcMain.handle('update-merchant-unionpay', async (_event, unionpayId: number, unionpayData: Omit<MerchantUnionpay, 'merchant_unionpay_id' | 'create_dt' | 'create_by'>): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log('✏️ Updating merchant UnionPay ID:', unionpayId);
      client = await getDbConnection();

      const query = `
        UPDATE merchant_unionpay
        SET merchant_id = $1, mdr_local = $2, mdr_normal = $3, mdr_premium = $4,
            mdr_diamond = $5, mdr_qrcode = $6, active = $7,
            update_by = $8, update_dt = CURRENT_TIMESTAMP
        WHERE merchant_unionpay_id = $9
        RETURNING merchant_unionpay_id, merchant_id, mdr_local, mdr_normal, mdr_premium,
                  mdr_diamond, mdr_qrcode, active, create_by, create_dt, update_by, update_dt
      `;

      const values = [
        unionpayData.merchant_id,
        unionpayData.mdr_local || null,
        unionpayData.mdr_normal || null,
        unionpayData.mdr_premium || null,
        unionpayData.mdr_diamond || null,
        unionpayData.mdr_qrcode || null,
        unionpayData.active,
        unionpayData.update_by || 'SYSTEM',
        unionpayId
      ];

      const result = await client.query(query, values);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'UnionPay settings not found',
          error: 'UNIONPAY_SETTINGS_NOT_FOUND'
        };
      }

      console.log('✅ Merchant UnionPay settings updated successfully');

      return {
        success: true,
        message: 'UnionPay settings updated successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error updating merchant UnionPay:', error.message);

      let errorMessage = 'Failed to update UnionPay settings';
      if (error.code === '23503') {
        errorMessage = 'Invalid merchant ID';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Delete merchant unionpay
  ipcMain.handle('delete-merchant-unionpay', async (_event, unionpayId: number): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log('🗑️ Deleting merchant UnionPay ID:', unionpayId);
      client = await getDbConnection();

      const query = `
        DELETE FROM merchant_unionpay
        WHERE merchant_unionpay_id = $1
        RETURNING merchant_unionpay_id
      `;

      const result = await client.query(query, [unionpayId]);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'UnionPay settings not found',
          error: 'UNIONPAY_SETTINGS_NOT_FOUND'
        };
      }

      console.log('✅ Merchant UnionPay settings deleted successfully');

      return {
        success: true,
        message: 'UnionPay settings deleted successfully'
      };

    } catch (error: any) {
      console.error('❌ Error deleting merchant UnionPay:', error.message);
      return {
        success: false,
        message: 'Failed to delete UnionPay settings',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get all merchant related data in a single call (OPTIMIZED)
  ipcMain.handle('get-merchant-complete-data', async (_event, merchantId: number): Promise<any> => {
    let client: Client | null = null;

    try {
      console.log(`🚀 Fetching complete data for merchant ID: ${merchantId} (OPTIMIZED)`);
      const startTime = performance.now();

      client = await getDbConnection();

      // Single transaction to get all related data
      await client.query('BEGIN');

      // Get bank accounts
      const bankQuery = `
        SELECT mb.merchant_bank_id, mb.merchant_id, mb.bank_id, mb.bank_account_no,
               mb.bank_account_name, mb.bank_branch_name, mb.active,
               mb.create_by, mb.create_dt, mb.update_by, mb.update_dt,
               b.bank_name_th, b.bank_name_en, b.bank_code
        FROM merchant_bank mb
        LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id
        WHERE mb.merchant_id = $1
        ORDER BY mb.merchant_bank_id
      `;

      // Get WeChat settings
      const wechatQuery = `
        SELECT merchant_wechat_id, merchant_id, wechat_rate, active,
               create_by, create_dt, update_by, update_dt
        FROM merchant_wechat
        WHERE merchant_id = $1
        ORDER BY merchant_wechat_id
      `;

      // Get UnionPay settings
      const unionpayQuery = `
        SELECT merchant_unionpay_id, merchant_id, mdr_local, mdr_normal, mdr_premium,
               mdr_diamond, mdr_qrcode, active, create_by, create_dt, update_by, update_dt
        FROM merchant_unionpay
        WHERE merchant_id = $1
        ORDER BY merchant_unionpay_id
      `;

      // Execute all queries in parallel within the transaction
      const [bankResult, wechatResult, unionpayResult] = await Promise.all([
        client.query(bankQuery, [merchantId]),
        client.query(wechatQuery, [merchantId]),
        client.query(unionpayQuery, [merchantId])
      ]);

      await client.query('COMMIT');

      const endTime = performance.now();
      console.log(`⚡ Complete data fetch completed in ${(endTime - startTime).toFixed(2)}ms`);
      console.log(`✅ Found ${bankResult.rows.length} banks, ${wechatResult.rows.length} WeChat, ${unionpayResult.rows.length} UnionPay`);

      return {
        success: true,
        message: 'Complete merchant data fetched successfully',
        data: {
          banks: bankResult.rows,
          wechat: wechatResult.rows.length > 0 ? wechatResult.rows[0] : null,
          unionpay: unionpayResult.rows.length > 0 ? unionpayResult.rows[0] : null
        }
      };

    } catch (error: any) {
      if (client) {
        await client.query('ROLLBACK');
      }
      console.error('❌ Error fetching complete merchant data:', error.message);
      return {
        success: false,
        message: 'Failed to fetch complete merchant data',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  console.log('✅ All Merchant handlers registered successfully');
}
